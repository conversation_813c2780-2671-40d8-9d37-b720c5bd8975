
import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Heart, Activity, Flower, Zap, Target, Sparkles } from "lucide-react";

const services = [
  {
    icon: Heart,
    title: "Fisioterapia Pélvica",
    description: "Tratamento especializado para fortalecimento do assoalho pélvico, incontinência urinária e preparação para o parto.",
    benefits: ["Fortalecimento muscular", "<PERSON><PERSON><PERSON> de dores", "Melhora da qualidade de vida"]
  },
  {
    icon: Activity,
    title: "Pilates Terapêutico",
    description: "Exercícios específicos para fortalecimento do core e assoalho pélvico, melhorando postura e consciência corporal.",
    benefits: ["Fortalecimento do core", "Melhora da postura", "Prevenção de lesões"]
  },
  {
    icon: Flower,
    title: "Yoga Hormonal",
    description: "Técnicas milenares adaptadas para equilibrar hormônios naturalmente, especialmente durante a menopausa.",
    benefits: ["Equilíbrio hormonal", "Redução de sintomas", "Bem-estar integral"]
  },
  {
    icon: Sparkles,
    title: "Aromaterapia",
    description: "Uso terapêutico de óleos essenciais para promover relaxamento e harmonização energética.",
    benefits: ["Relaxamento profundo", "Alívio do estresse", "Harmonização energética"]
  },
  {
    icon: Zap,
    title: "Acupuntura",
    description: "Medicina tradicional chinesa para tratamento de dores, ansiedade e desequilíbrios hormonais.",
    benefits: ["Alívio de dores", "Redução da ansiedade", "Equilíbrio energético"]
  },
  {
    icon: Target,
    title: "Tratamento Personalizado",
    description: "Avaliação completa e plano terapêutico individualizado para suas necessidades específicas.",
    benefits: ["Abordagem única", "Resultados efetivos", "Acompanhamento contínuo"]
  }
];

const ServicesSection = () => {
  return (
    <section id="services" className="section-spacing">
      <div className="max-w-7xl mx-auto container-spacing">
        <div className="text-center mb-8 sm:mb-12 md:mb-16 animate-fade-in">
          <div className="badge-pattern inline-flex items-center mb-4 md:mb-6">
            🌸 Nossos Tratamentos
          </div>
          
          <h2 className="heading-pattern mb-4 md:mb-6">
            Cuidados <span className="text-logo-rose">Especializados</span>
          </h2>
          
          <p className="subheading-pattern">
            Oferecemos uma abordagem integrativa e personalizada, combinando as melhores 
            técnicas tradicionais e modernas para o seu bem-estar completo.
          </p>
        </div>
        
        {/* Desktop Grid */}
        <div className="hidden lg:grid lg:grid-cols-3 gap-6 lg:gap-8">
          {services.map((service, index) => (
            <Card 
              key={index}
              className="service-card animate-fade-in"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <CardContent className="p-4 sm:p-6 lg:p-8">
                <div className="icon-container bg-logo-rose/10 mb-4 md:mb-6">
                  <service.icon className="h-5 w-5 md:h-6 md:w-6 text-logo-rose" />
                </div>
                
                <h3 className="text-lg md:text-xl font-logo font-bold text-gray-800 mb-3 md:mb-4">
                  {service.title}
                </h3>
                
                <p className="text-gray-600 leading-relaxed mb-4 md:mb-6 text-sm md:text-base">
                  {service.description}
                </p>
                
                <div className="space-y-2">
                  {service.benefits.map((benefit, benefitIndex) => (
                    <div key={benefitIndex} className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-logo-rose rounded-full"></div>
                      <span className="text-sm text-gray-600">{benefit}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Mobile/Tablet Carousel */}
        <div className="lg:hidden animate-fade-in">
          <Carousel className="w-full">
            <CarouselContent className="-ml-2 sm:-ml-4">
              {services.map((service, index) => (
                <CarouselItem key={index} className="pl-2 sm:pl-4 basis-full sm:basis-1/2">
                  <div className="p-1">
                    <Card className="service-card h-full">
                      <CardContent className="p-4 sm:p-6">
                        <div className="icon-container bg-logo-rose/10 mb-4">
                          <service.icon className="h-5 w-5 sm:h-6 sm:w-6 text-logo-rose" />
                        </div>
                        
                        <h3 className="text-lg sm:text-xl font-logo font-bold text-gray-800 mb-3">
                          {service.title}
                        </h3>
                        
                        <p className="text-gray-600 leading-relaxed mb-4 text-sm sm:text-base">
                          {service.description}
                        </p>
                        
                        <div className="space-y-2">
                          {service.benefits.map((benefit, benefitIndex) => (
                            <div key={benefitIndex} className="flex items-center space-x-2">
                              <div className="w-2 h-2 bg-logo-rose rounded-full"></div>
                              <span className="text-xs sm:text-sm text-gray-600">{benefit}</span>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="hidden sm:flex left-2 border-0 bg-white/80 hover:bg-white" />
            <CarouselNext className="hidden sm:flex right-2 border-0 bg-white/80 hover:bg-white" />
          </Carousel>
        </div>
        
        <div className="text-center mt-8 sm:mt-10 md:mt-12">
          <p className="text-sm sm:text-base md:text-lg text-gray-600 mb-4 md:mb-6">
            Interessada em algum tratamento específico?
          </p>
          <button 
            onClick={() =>
              document
                .getElementById("contact")
                ?.scrollIntoView({ behavior: "smooth" })
            }
            className="button-primary"
          >
            Fale Comigo
          </button>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
