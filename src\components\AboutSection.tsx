
import { Card, CardContent } from "@/components/ui/card";
import { Award, Heart, Users, Clock, CheckCircle, Star } from "lucide-react";

const achievements = [
  { icon: Users, value: "500+", label: "Pacientes Atendidas" },
  { icon: Clock, value: "8+", label: "Anos de Experiência" },
  { icon: Heart, value: "98%", label: "Taxa de Satisfação" },
  { icon: Award, value: "15+", label: "Especializações" }
];

const qualifications = [
  "Formada em Fisioterapia pela USP",
  "Especialização em Fisioterapia Pélvica",
  "Certificação em Yoga Hormonal",
  "Formação em Aromaterapia Clínica",
  "Acupuntura Sistêmica",
  "Pilates Terapêutico Avançado"
];

const AboutSection = () => {
  return (
    <section id="about" className="section-spacing bg-gradient-to-br from-logo-rose/3 to-white/50">
      <div className="max-w-7xl mx-auto container-spacing">
        <div className="text-center mb-8 sm:mb-12 md:mb-16 animate-fade-in">
          <div className="badge-pattern inline-flex items-center mb-4 md:mb-6">
            👩‍⚕️ Sobre a Profissional
          </div>
          
          <h2 className="heading-pattern mb-4 md:mb-6">
            Conheça <span className="text-logo-rose">Mariah Blaj</span>
          </h2>
          
          <p className="subheading-pattern">
            Fisioterapeuta especializada em saúde pélvica feminina, 
            dedicada a transformar vidas através do cuidado integral e humanizado.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center">
          <div className="space-y-6 md:space-y-8 animate-fade-in">
            <div className="space-y-4 md:space-y-6 text-sm sm:text-base md:text-lg text-gray-600 leading-relaxed">
              <p>
                Com mais de 8 anos de experiência, desenvolvi uma abordagem única que 
                combina técnicas modernas de fisioterapia com terapias complementares, 
                sempre priorizando o conforto e a privacidade das minhas pacientes.
              </p>
              <p>
                Acredito que cada mulher merece se sentir plena, confiante e livre de 
                desconfortos. Meu compromisso é oferecer um ambiente acolhedor onde você 
                possa se expressar livremente e encontrar soluções efetivas.
              </p>
            </div>
            
            <div>
              <h3 className="text-lg sm:text-xl md:text-2xl font-logo font-semibold text-gray-800 mb-3 md:mb-4">
                Formação e Especializações
              </h3>
              <div className="grid gap-2 md:gap-3">
                {qualifications.map((qualification, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-logo-rose flex-shrink-0" />
                    <span className="text-gray-700 text-xs sm:text-sm md:text-base">{qualification}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          <div className="relative animate-fade-in">
            <div className="relative z-10">
              <img 
                src="https://images.unsplash.com/photo-1649972904349-6e44c42644a7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Mariah Blaj - Fisioterapeuta Pélvica"
                className="w-full h-64 sm:h-72 md:h-80 lg:h-96 object-cover rounded-3xl"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-2 sm:gap-3 md:gap-4 mt-4 sm:mt-6 md:mt-8">
              {achievements.map((achievement, index) => (
                <Card key={index} className="card-pattern">
                  <CardContent className="p-3 sm:p-4 md:p-6 text-center">
                    <div className="icon-container bg-logo-rose/10 mx-auto mb-2 md:mb-3">
                      <achievement.icon className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 text-logo-rose" />
                    </div>
                    <div className="text-lg sm:text-xl md:text-2xl font-bold text-logo-rose mb-1">
                      {achievement.value}
                    </div>
                    <div className="text-xs sm:text-sm text-gray-600">{achievement.label}</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
