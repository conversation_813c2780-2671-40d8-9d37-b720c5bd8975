
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-smoothing: antialiased;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Enhanced animations for better UX */
@layer utilities {
  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes float {
    0%, 100% { 
      transform: translateY(0px); 
    }
    50% { 
      transform: translateY(-10px); 
    }
  }

  @keyframes scale-in {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes slide-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fade-in 0.6s ease-out forwards;
  }

  .animate-float {
    animation: float 4s ease-in-out infinite;
  }

  .animate-scale-in {
    animation: scale-in 0.4s ease-out forwards;
  }

  .animate-slide-up {
    animation: slide-up 0.5s ease-out forwards;
  }

  /* Blur background effect */
  .blur-background {
    @apply fixed inset-0 -z-10 bg-gradient-to-br from-logo-rose/5 via-white to-logo-emerald/5 backdrop-blur-3xl;
  }

  /* Component patterns using dark rose as primary */
  .card-pattern {
    @apply bg-white/90 backdrop-blur-sm border border-logo-rose/20 rounded-3xl transition-all duration-300 hover:bg-white/95 hover:scale-[1.02];
  }

  .button-primary {
    @apply bg-logo-rose hover:bg-logo-rose/90 text-white px-4 py-2 sm:px-6 sm:py-3 rounded-3xl font-medium transition-all duration-300 border-0 hover:scale-105;
  }

  .button-secondary {
    @apply border-2 border-logo-rose text-logo-rose hover:bg-logo-rose/5 px-4 py-2 sm:px-6 sm:py-3 rounded-3xl font-medium transition-all duration-300;
  }

  .badge-pattern {
    @apply bg-logo-rose/10 text-logo-rose px-3 py-1.5 sm:px-4 sm:py-2 rounded-full text-xs sm:text-sm font-medium border border-logo-rose/20;
  }

  /* Mobile-optimized spacing */
  .section-spacing {
    @apply py-8 px-3 sm:py-12 sm:px-4 md:py-16 lg:py-20;
  }

  .container-spacing {
    @apply px-3 sm:px-4 md:px-6 lg:px-8;
  }

  .heading-pattern {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-logo font-bold text-gray-800 leading-tight;
  }

  .subheading-pattern {
    @apply text-sm sm:text-base md:text-lg lg:text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto;
  }

  .icon-container {
    @apply w-10 h-10 sm:w-12 sm:h-12 rounded-3xl flex items-center justify-center transition-all duration-300;
  }

  /* Mobile-optimized card patterns */
  .service-card {
    @apply bg-white/80 border border-logo-rose/15 rounded-3xl p-4 sm:p-6 lg:p-8 transition-all duration-300 hover:bg-white/90 hover:border-logo-rose/25 hover:scale-[1.02];
  }

  .testimonial-card {
    @apply bg-white/70 border border-logo-rose/10 rounded-3xl p-4 sm:p-6 md:p-8 transition-all duration-300 hover:bg-white/80 hover:border-logo-rose/20;
  }

  /* Remove shadows for flat design */
  * {
    box-shadow: none !important;
  }

  /* Mobile-responsive grid */
  .mobile-grid {
    @apply grid grid-cols-1 gap-4 sm:gap-6 md:grid-cols-2 lg:grid-cols-3 lg:gap-8;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar with logo colors */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--background));
}

::-webkit-scrollbar-thumb {
  background: theme('colors.logo-rose');
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: theme('colors.logo-rose');
}

/* Consistent focus styles */
*:focus-visible {
  outline: 2px solid theme('colors.logo-rose');
  outline-offset: 2px;
  border-radius: 4px;
}
