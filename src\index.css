@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-smoothing: antialiased;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Enhanced animations for better UX */
@layer utilities {
  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(20px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes float {

    0%,
    100% {
      transform: translateY(0px);
    }

    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes scale-in {
    from {
      opacity: 0;
      transform: scale(0.95);
    }

    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes slide-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fade-in 0.6s ease-out forwards;
  }

  .animate-float {
    animation: float 4s ease-in-out infinite;
  }

  .animate-scale-in {
    animation: scale-in 0.4s ease-out forwards;
  }

  .animate-slide-up {
    animation: slide-up 0.5s ease-out forwards;
  }

  /* Blur background effect */
  .blur-background {
    @apply fixed inset-0 -z-10 bg-gradient-to-br from-logo-rose/5 via-white to-logo-emerald/5 backdrop-blur-3xl;
  }

  /* Enhanced component patterns with depth and sophistication */
  .card-pattern {
    @apply bg-white/95 backdrop-blur-sm border border-brand-rose-200/30 rounded-3xl transition-all duration-300 hover:bg-white hover:scale-[1.02] shadow-sm hover:shadow-md;
  }

  .card-elevated {
    @apply bg-white border border-brand-neutral-200/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300;
  }

  .card-glass {
    @apply bg-white/80 backdrop-blur-md border border-white/20 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300;
  }

  .button-primary {
    @apply bg-brand-rose-500 hover:bg-brand-rose-600 text-white px-4 py-2 sm:px-6 sm:py-3 rounded-3xl font-medium transition-all duration-300 border-0 hover:scale-105 shadow-brand-sm hover:shadow-brand;
  }

  .button-secondary {
    @apply border-2 border-brand-rose-500 text-brand-rose-600 hover:bg-brand-rose-50 px-4 py-2 sm:px-6 sm:py-3 rounded-3xl font-medium transition-all duration-300 hover:scale-105;
  }

  .button-emerald {
    @apply bg-brand-emerald-500 hover:bg-brand-emerald-600 text-white px-4 py-2 sm:px-6 sm:py-3 rounded-3xl font-medium transition-all duration-300 border-0 hover:scale-105 shadow-emerald-sm hover:shadow-emerald;
  }

  .button-ghost {
    @apply text-brand-neutral-700 hover:bg-brand-neutral-100 px-4 py-2 sm:px-6 sm:py-3 rounded-3xl font-medium transition-all duration-300 hover:scale-105;
  }

  .badge-pattern {
    @apply bg-brand-rose-50 text-brand-rose-700 px-3 py-1.5 sm:px-4 sm:py-2 rounded-full text-xs sm:text-sm font-medium border border-brand-rose-200/50 shadow-xs;
  }

  .badge-emerald {
    @apply bg-brand-emerald-50 text-brand-emerald-700 px-3 py-1.5 sm:px-4 sm:py-2 rounded-full text-xs sm:text-sm font-medium border border-brand-emerald-200/50 shadow-xs;
  }

  .badge-neutral {
    @apply bg-brand-neutral-100 text-brand-neutral-700 px-3 py-1.5 sm:px-4 sm:py-2 rounded-full text-xs sm:text-sm font-medium border border-brand-neutral-200/50 shadow-xs;
  }

  /* Mobile-optimized spacing */
  .section-spacing {
    @apply py-8 px-3 sm:py-12 sm:px-4 md:py-16 lg:py-20;
  }

  .container-spacing {
    @apply px-3 sm:px-4 md:px-6 lg:px-8;
  }

  /* Enhanced typography system */
  .heading-pattern {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-heading font-bold text-brand-neutral-800 leading-tight tracking-tight;
  }

  .heading-xl {
    @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-heading font-bold text-brand-neutral-800 leading-tight tracking-tight;
  }

  .heading-lg {
    @apply text-xl sm:text-2xl md:text-3xl lg:text-4xl font-heading font-bold text-brand-neutral-800 leading-tight tracking-tight;
  }

  .heading-md {
    @apply text-lg sm:text-xl md:text-2xl font-heading font-semibold text-brand-neutral-800 leading-tight;
  }

  .heading-sm {
    @apply text-base sm:text-lg md:text-xl font-heading font-semibold text-brand-neutral-800 leading-tight;
  }

  .subheading-pattern {
    @apply text-sm sm:text-base md:text-lg lg:text-xl text-brand-neutral-600 leading-relaxed max-w-3xl mx-auto font-body;
  }

  .text-body-lg {
    @apply text-base md:text-lg text-brand-neutral-600 leading-relaxed font-body;
  }

  .text-body {
    @apply text-sm md:text-base text-brand-neutral-600 leading-relaxed font-body;
  }

  .text-body-sm {
    @apply text-xs md:text-sm text-brand-neutral-600 leading-relaxed font-body;
  }

  .text-muted {
    @apply text-brand-neutral-500 font-body;
  }

  .text-accent {
    @apply text-brand-rose-600 font-medium;
  }

  .text-accent-emerald {
    @apply text-brand-emerald-600 font-medium;
  }

  .icon-container {
    @apply w-10 h-10 sm:w-12 sm:h-12 rounded-3xl flex items-center justify-center transition-all duration-300;
  }

  /* Enhanced card patterns with depth */
  .service-card {
    @apply bg-white/95 border border-brand-rose-200/30 rounded-3xl p-4 sm:p-6 lg:p-8 transition-all duration-300 hover:bg-white hover:border-brand-rose-300/40 hover:scale-[1.02] shadow-sm hover:shadow-md;
  }

  .testimonial-card {
    @apply bg-white/90 border border-brand-rose-200/20 rounded-3xl p-4 sm:p-6 md:p-8 transition-all duration-300 hover:bg-white hover:border-brand-rose-300/30 shadow-sm hover:shadow-lg;
  }

  .feature-card {
    @apply bg-white border border-brand-neutral-200/50 rounded-2xl p-6 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg;
  }

  .gallery-card {
    @apply bg-white border border-brand-neutral-200/30 rounded-2xl overflow-hidden transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-xl;
  }

  /* Enhanced layout utilities */
  .mobile-grid {
    @apply grid grid-cols-1 gap-4 sm:gap-6 md:grid-cols-2 lg:grid-cols-3 lg:gap-8;
  }

  .responsive-grid-2 {
    @apply grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8;
  }

  .responsive-grid-3 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8;
  }

  .responsive-grid-4 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8;
  }

  /* Enhanced spacing utilities */
  .section-spacing-sm {
    @apply py-6 px-3 sm:py-8 sm:px-4 md:py-10 lg:py-12;
  }

  .section-spacing-lg {
    @apply py-12 px-3 sm:py-16 sm:px-4 md:py-20 lg:py-24;
  }

  .container-spacing-sm {
    @apply px-2 sm:px-3 md:px-4 lg:px-6;
  }

  .container-spacing-lg {
    @apply px-4 sm:px-6 md:px-8 lg:px-12;
  }

  /* Interactive utilities */
  .interactive-scale {
    @apply transition-transform duration-300 hover:scale-105 active:scale-95;
  }

  .interactive-lift {
    @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-lg;
  }

  .interactive-glow {
    @apply transition-all duration-300 hover:shadow-brand-lg;
  }

  .interactive-emerald-glow {
    @apply transition-all duration-300 hover:shadow-emerald-lg;
  }

  /* Loading and skeleton utilities */
  .skeleton {
    @apply animate-pulse bg-brand-neutral-200 rounded;
  }

  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-brand-neutral-300 border-t-brand-rose-500;
  }

  .loading-dots::after {
    content: '';
    animation: loading-dots 1.5s infinite;
  }

  @keyframes loading-dots {

    0%,
    20% {
      content: '.';
    }

    40% {
      content: '..';
    }

    60%,
    100% {
      content: '...';
    }
  }

  /* Focus and accessibility utilities */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-brand-rose-500 focus:ring-offset-2;
  }

  .focus-ring-emerald {
    @apply focus:outline-none focus:ring-2 focus:ring-brand-emerald-500 focus:ring-offset-2;
  }

  /* Gradient utilities */
  .gradient-rose {
    @apply bg-gradient-to-r from-brand-rose-500 to-brand-rose-600;
  }

  .gradient-emerald {
    @apply bg-gradient-to-r from-brand-emerald-500 to-brand-emerald-600;
  }

  .gradient-rose-emerald {
    @apply bg-gradient-to-r from-brand-rose-500 to-brand-emerald-500;
  }

  .gradient-text-rose {
    @apply bg-gradient-to-r from-brand-rose-500 to-brand-rose-600 bg-clip-text text-transparent;
  }

  .gradient-text-emerald {
    @apply bg-gradient-to-r from-brand-emerald-500 to-brand-emerald-600 bg-clip-text text-transparent;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar with logo colors */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--background));
}

::-webkit-scrollbar-thumb {
  background: theme('colors.logo-rose');
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: theme('colors.logo-rose');
}

/* Consistent focus styles */
*:focus-visible {
  outline: 2px solid theme('colors.logo-rose');
  outline-offset: 2px;
  border-radius: 4px;
}