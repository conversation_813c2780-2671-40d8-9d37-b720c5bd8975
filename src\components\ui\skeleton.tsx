import { cn } from "@/lib/utils";

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "text" | "circular" | "rectangular";
  width?: string | number;
  height?: string | number;
  lines?: number;
}

const Skeleton = ({
  className,
  variant = "default",
  width,
  height,
  lines = 1,
  ...props
}: SkeletonProps) => {
  const baseClasses = "skeleton";

  const variantClasses = {
    default: "rounded-2xl",
    text: "rounded-lg h-4",
    circular: "rounded-full",
    rectangular: "rounded-lg",
  };

  const style = {
    width: typeof width === "number" ? `${width}px` : width,
    height: typeof height === "number" ? `${height}px` : height,
  };

  if (variant === "text" && lines > 1) {
    return (
      <div className="space-y-2" {...props}>
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={cn(
              baseClasses,
              variantClasses.text,
              index === lines - 1 && "w-3/4", // Last line is shorter
              className
            )}
            style={style}
          />
        ))}
      </div>
    );
  }

  return (
    <div
      className={cn(baseClasses, variantClasses[variant], className)}
      style={style}
      {...props}
    />
  );
};

// Predefined skeleton components for common use cases
const SkeletonCard = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div className={cn("card-pattern p-6 space-y-4", className)} {...props}>
    <div className="flex items-center space-x-4">
      <Skeleton variant="circular" width={48} height={48} />
      <div className="flex-1 space-y-2">
        <Skeleton variant="text" width="60%" />
        <Skeleton variant="text" width="40%" />
      </div>
    </div>
    <Skeleton variant="text" lines={3} />
    <div className="flex space-x-2">
      <Skeleton variant="rectangular" width={80} height={32} />
      <Skeleton variant="rectangular" width={100} height={32} />
    </div>
  </div>
);

const SkeletonText = ({
  lines = 3,
  className,
  ...props
}: { lines?: number } & React.HTMLAttributes<HTMLDivElement>) => (
  <div className={cn("space-y-2", className)} {...props}>
    <Skeleton variant="text" lines={lines} />
  </div>
);

const SkeletonAvatar = ({
  size = 48,
  className,
  ...props
}: { size?: number } & React.HTMLAttributes<HTMLDivElement>) => (
  <Skeleton
    variant="circular"
    width={size}
    height={size}
    className={className}
    {...props}
  />
);

const SkeletonButton = ({
  width = 120,
  className,
  ...props
}: { width?: number | string } & React.HTMLAttributes<HTMLDivElement>) => (
  <Skeleton
    variant="rectangular"
    width={width}
    height={40}
    className={cn("rounded-3xl", className)}
    {...props}
  />
);

export { Skeleton, SkeletonCard, SkeletonText, SkeletonAvatar, SkeletonButton };
