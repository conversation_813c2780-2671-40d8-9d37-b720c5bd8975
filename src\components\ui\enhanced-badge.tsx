import * as React from "react";
import { cn } from "@/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";
import { X } from "lucide-react";

const badgeVariants = cva(
  "inline-flex items-center rounded-full border font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "badge-pattern",
        emerald: "badge-emerald",
        neutral: "badge-neutral",
        success: "bg-success-50 text-success-700 border-success-200/50 shadow-xs",
        warning: "bg-warning-50 text-warning-700 border-warning-200/50 shadow-xs",
        error: "bg-error-50 text-error-700 border-error-200/50 shadow-xs",
        outline: "bg-transparent border-brand-neutral-300 text-brand-neutral-700 hover:bg-brand-neutral-50",
        ghost: "bg-transparent border-transparent text-brand-neutral-600 hover:bg-brand-neutral-100",
        gradient: "bg-gradient-to-r from-brand-rose-500 to-brand-rose-600 text-white border-transparent shadow-brand-sm",
        "gradient-emerald": "bg-gradient-to-r from-brand-emerald-500 to-brand-emerald-600 text-white border-transparent shadow-emerald-sm",
      },
      size: {
        sm: "px-2 py-1 text-xs",
        default: "px-3 py-1.5 text-sm",
        lg: "px-4 py-2 text-base",
      },
      interactive: {
        true: "cursor-pointer hover:scale-105 active:scale-95",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      interactive: false,
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onRemove?: () => void;
  removable?: boolean;
}

const EnhancedBadge = React.forwardRef<HTMLDivElement, BadgeProps>(
  (
    {
      className,
      variant,
      size,
      interactive,
      leftIcon,
      rightIcon,
      onRemove,
      removable = false,
      children,
      onClick,
      ...props
    },
    ref
  ) => {
    const isInteractive = interactive || !!onClick;

    return (
      <div
        ref={ref}
        className={cn(badgeVariants({ variant, size, interactive: isInteractive, className }))}
        onClick={onClick}
        role={onClick ? "button" : undefined}
        tabIndex={onClick ? 0 : undefined}
        onKeyDown={
          onClick
            ? (e) => {
                if (e.key === "Enter" || e.key === " ") {
                  e.preventDefault();
                  onClick(e as any);
                }
              }
            : undefined
        }
        {...props}
      >
        {leftIcon && (
          <span className="mr-1.5 flex-shrink-0" aria-hidden="true">
            {leftIcon}
          </span>
        )}
        
        <span className="truncate">{children}</span>
        
        {rightIcon && !removable && (
          <span className="ml-1.5 flex-shrink-0" aria-hidden="true">
            {rightIcon}
          </span>
        )}
        
        {(removable || onRemove) && (
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              onRemove?.();
            }}
            className="ml-1.5 flex-shrink-0 hover:bg-black/10 rounded-full p-0.5 transition-colors"
            aria-label="Remove"
          >
            <X className="h-3 w-3" />
          </button>
        )}
      </div>
    );
  }
);

EnhancedBadge.displayName = "EnhancedBadge";

// Specialized badge components
const StatusBadge = React.forwardRef<
  HTMLDivElement,
  Omit<BadgeProps, "variant"> & { status: "success" | "warning" | "error" | "info" }
>(({ status, ...props }, ref) => {
  const variantMap = {
    success: "success" as const,
    warning: "warning" as const,
    error: "error" as const,
    info: "neutral" as const,
  };

  return (
    <EnhancedBadge
      ref={ref}
      variant={variantMap[status]}
      {...props}
    />
  );
});
StatusBadge.displayName = "StatusBadge";

const CountBadge = React.forwardRef<
  HTMLDivElement,
  Omit<BadgeProps, "size" | "children"> & { count: number; max?: number }
>(({ count, max = 99, ...props }, ref) => {
  const displayCount = count > max ? `${max}+` : count.toString();
  
  return (
    <EnhancedBadge
      ref={ref}
      size="sm"
      variant="gradient"
      {...props}
    >
      {displayCount}
    </EnhancedBadge>
  );
});
CountBadge.displayName = "CountBadge";

const CategoryBadge = React.forwardRef<
  HTMLDivElement,
  Omit<BadgeProps, "variant"> & { category: string; color?: "rose" | "emerald" | "neutral" }
>(({ category, color = "neutral", ...props }, ref) => {
  const variantMap = {
    rose: "default" as const,
    emerald: "emerald" as const,
    neutral: "neutral" as const,
  };

  return (
    <EnhancedBadge
      ref={ref}
      variant={variantMap[color]}
      {...props}
    >
      {category}
    </EnhancedBadge>
  );
});
CategoryBadge.displayName = "CategoryBadge";

export {
  EnhancedBadge,
  StatusBadge,
  CountBadge,
  CategoryBadge,
  badgeVariants,
};
