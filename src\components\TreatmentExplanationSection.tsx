
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Heart, Activity, Flower, CheckCircle, ArrowRight, Clock, Users } from "lucide-react";

const treatmentDetails = [
  {
    icon: Heart,
    title: "Fisioterapia Pélvica",
    shortDesc: "Fortalecimento e reabilitação do assoalho pélvico",
    benefits: [
      "Fortalecimento do assoalho pélvico",
      "Melhoria da incontinência urinária",
      "Tratamento de disfunções sexuais",
      "Alívio de dores pélvicas",
      "Preparação pré e pós-parto"
    ],
    duration: "60 minutos",
    frequency: "1-2x por semana"
  },
  {
    icon: Activity,
    title: "Pilates Terapêutico",
    shortDesc: "Fortalecimento do core e estabilização corporal",
    benefits: [
      "Fortalecimento do core profundo",
      "Melhoria da postura corporal",
      "Redução de dores nas costas",
      "Aumento da flexibilidade",
      "Consciência corporal"
    ],
    duration: "50 minutos",
    frequency: "2-3x por semana"
  },
  {
    icon: Flower,
    title: "Yoga Hormonal",
    shortDesc: "Equilíbrio natural dos hormônios femininos",
    benefits: [
      "Regulação do ciclo menstrual",
      "Alívio dos sintomas da menopausa",
      "Redução do estresse e ansiedade",
      "Melhoria do sono",
      "Aumento da energia vital"
    ],
    duration: "75 minutos",
    frequency: "2x por semana"
  }
];

const TreatmentExplanationSection = () => {
  return (
    <section className="section-spacing bg-gradient-to-br from-logo-rose/5 to-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="heading-pattern mb-6">
            Como Funcionam Nossos <span className="text-logo-emerald">Tratamentos</span>
          </h2>
          <p className="subheading-pattern">
            Entenda em detalhes como cada tratamento pode transformar sua qualidade de vida 
            através de técnicas comprovadas e cuidado personalizado.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {treatmentDetails.map((treatment, index) => (
            <Card 
              key={index}
              className="card-pattern bg-white/90 animate-fade-in"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <CardContent className="p-8">
                <div className="text-center mb-6">
                  <div className="icon-container bg-logo-emerald/10 mx-auto mb-4">
                    <treatment.icon className="h-8 w-8 text-logo-emerald" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2">{treatment.title}</h3>
                  <p className="text-gray-600 text-sm">{treatment.shortDesc}</p>
                </div>

                <div className="space-y-4 mb-6">
                  <h4 className="font-semibold text-gray-800 text-center">Benefícios:</h4>
                  <ul className="space-y-2">
                    {treatment.benefits.slice(0, 3).map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="flex items-start space-x-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-logo-emerald mt-0.5 flex-shrink-0" />
                        <span className="text-gray-600">{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="space-y-3 mb-6 p-4 bg-logo-emerald/5 rounded-2xl">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-logo-emerald" />
                      <span className="text-gray-600">Duração:</span>
                    </div>
                    <span className="font-semibold text-gray-800">{treatment.duration}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-logo-emerald" />
                      <span className="text-gray-600">Frequência:</span>
                    </div>
                    <span className="font-semibold text-gray-800">{treatment.frequency}</span>
                  </div>
                </div>

                <Button 
                  className="button-primary w-full"
                  onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                >
                  Agendar Consulta
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <div className="inline-flex items-center space-x-4 bg-logo-emerald/5 px-8 py-4 rounded-3xl">
            <div className="text-center">
              <div className="text-2xl font-bold text-logo-emerald">500+</div>
              <div className="text-sm text-gray-600">Pacientes</div>
            </div>
            <div className="w-px h-8 bg-logo-emerald/20"></div>
            <div className="text-center">
              <div className="text-2xl font-bold text-logo-emerald">98%</div>
              <div className="text-sm text-gray-600">Satisfação</div>
            </div>
            <div className="w-px h-8 bg-logo-emerald/20"></div>
            <div className="text-center">
              <div className="text-2xl font-bold text-logo-emerald">8+</div>
              <div className="text-sm text-gray-600">Anos</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TreatmentExplanationSection;
