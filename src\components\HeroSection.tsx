
import {
  ArrowR<PERSON>,
  Phone,
  Heart,
  Star,
  Award,
  Users,
  CheckCircle,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const HeroSection = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center section-spacing">
      <div className="max-w-7xl mx-auto container-spacing grid lg:grid-cols-2 gap-8 lg:gap-16 items-center">
        <div className="space-y-4 sm:space-y-6 md:space-y-8 animate-fade-in text-center lg:text-left">
          <div className="space-y-3 sm:space-y-4 md:space-y-6">
            <div className="badge-pattern inline-flex items-center">
              ✨ Especialista em Fisioterapia Pélvica
            </div>
            
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-logo font-bold text-gray-800 leading-tight">
              <span className="text-logo-rose"><PERSON><PERSON></span>
              <br />
              <span className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl text-gray-600 font-sans font-normal">
                Fisioterapeuta Pélvica
              </span>
            </h1>
            
            <p className="subheading-pattern mx-auto lg:mx-0">
              Cuidado especializado e humanizado para sua saúde íntima, 
              com técnicas modernas e abordagem personalizada.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center lg:justify-start">
            <Button
              size="lg"
              className="button-primary text-sm sm:text-base"
              onClick={() =>
                document
                  .getElementById("contact")
                  ?.scrollIntoView({ behavior: "smooth" })
              }
            >
              Agendar Consulta
              <ArrowRight className="ml-2 h-4 w-4 sm:h-5 sm:w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="button-secondary text-sm sm:text-base"
              onClick={() =>
                document
                  .getElementById("services")
                  ?.scrollIntoView({ behavior: "smooth" })
              }
            >
              Conhecer Tratamentos
            </Button>
          </div>

          <div className="grid grid-cols-3 gap-3 sm:gap-4 md:gap-6 pt-4 sm:pt-6 md:pt-8">
            <div className="text-center">
              <div className="text-xl sm:text-2xl md:text-3xl font-bold text-logo-rose mb-1">500+</div>
              <div className="text-xs sm:text-sm text-gray-600">Pacientes</div>
            </div>
            <div className="text-center">
              <div className="text-xl sm:text-2xl md:text-3xl font-bold text-logo-rose mb-1">8+</div>
              <div className="text-xs sm:text-sm text-gray-600">Anos</div>
            </div>
            <div className="text-center">
              <div className="text-xl sm:text-2xl md:text-3xl font-bold text-logo-rose mb-1">98%</div>
              <div className="text-xs sm:text-sm text-gray-600">Satisfação</div>
            </div>
          </div>
        </div>

        <div className="relative animate-fade-in order-first lg:order-last">
          <div className="relative z-10 card-pattern p-3 sm:p-4 md:p-6">
            <img
              src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
              alt="Mariah Blaj - Fisioterapeuta Pélvica"
              className="w-full h-64 sm:h-72 md:h-80 lg:h-96 object-cover rounded-2xl"
            />
            <div className="absolute -bottom-3 -right-3 sm:-bottom-4 sm:-right-4 bg-logo-rose text-white p-3 sm:p-4 md:p-6 rounded-2xl">
              <p className="font-semibold text-sm sm:text-base md:text-lg">São Paulo</p>
              <p className="text-rose-100 text-xs sm:text-sm">
                Atendimento Especializado
              </p>
            </div>
          </div>
          
          <div className="absolute -top-4 -left-4 sm:-top-6 sm:-left-6 w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 bg-logo-rose/20 rounded-3xl animate-float" />
          <div className="absolute -bottom-4 -right-4 sm:-bottom-6 sm:-right-6 w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 bg-logo-emerald/20 rounded-3xl animate-float" style={{animationDelay: '2s'}} />
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
