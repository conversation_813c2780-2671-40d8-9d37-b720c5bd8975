import {
  ArrowR<PERSON>,
  Phone,
  Heart,
  Star,
  Award,
  Users,
  CheckCircle,
  Calendar,
  MapPin,
} from "lucide-react";
import { EnhancedButton } from "@/components/ui/enhanced-button";
import { EnhancedBadge } from "@/components/ui/enhanced-badge";
import { FeatureCard } from "@/components/ui/enhanced-card";

const HeroSection = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center section-spacing">
      <div className="max-w-7xl mx-auto container-spacing grid lg:grid-cols-2 gap-8 lg:gap-16 items-center">
        <div className="space-y-4 sm:space-y-6 md:space-y-8 animate-fade-in text-center lg:text-left">
          <div className="space-y-4 sm:space-y-6 md:space-y-8">
            <EnhancedBadge
              variant="gradient"
              size="lg"
              leftIcon={<Star className="h-4 w-4" />}
              className="inline-flex"
            >
              Especialista em Fisioterapia Pélvica
            </EnhancedBadge>

            <h1 className="heading-xl">
              <span className="gradient-text-rose"><PERSON>h Blaj</span>
              <br />
              <span className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl text-brand-neutral-600 font-body font-normal">
                Fisioterapeuta Pélvica
              </span>
            </h1>

            <p className="subheading-pattern mx-auto lg:mx-0">
              Cuidado especializado e humanizado para sua saúde íntima, com
              técnicas modernas e abordagem personalizada.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center lg:justify-start">
            <EnhancedButton
              variant="primary"
              size="lg"
              rightIcon={<ArrowRight className="h-5 w-5" />}
              onClick={() =>
                document
                  .getElementById("contact")
                  ?.scrollIntoView({ behavior: "smooth" })
              }
            >
              Agendar Consulta
            </EnhancedButton>
            <EnhancedButton
              variant="secondary"
              size="lg"
              onClick={() =>
                document
                  .getElementById("services")
                  ?.scrollIntoView({ behavior: "smooth" })
              }
            >
              Conhecer Tratamentos
            </EnhancedButton>
          </div>

          {/* Enhanced Stats Section */}
          <div className="grid grid-cols-3 gap-4 sm:gap-6 pt-6 sm:pt-8 border-t border-brand-neutral-200/30">
            <FeatureCard className="text-center p-4">
              <div className="flex flex-col items-center space-y-2">
                <div className="icon-container bg-brand-emerald-100 text-brand-emerald-600">
                  <Users className="h-5 w-5" />
                </div>
                <span className="heading-md text-brand-neutral-800">500+</span>
                <p className="text-body-sm text-muted">Pacientes</p>
              </div>
            </FeatureCard>

            <FeatureCard className="text-center p-4">
              <div className="flex flex-col items-center space-y-2">
                <div className="icon-container bg-brand-emerald-100 text-brand-emerald-600">
                  <Award className="h-5 w-5" />
                </div>
                <span className="heading-md text-brand-neutral-800">8+</span>
                <p className="text-body-sm text-muted">Anos</p>
              </div>
            </FeatureCard>

            <FeatureCard className="text-center p-4">
              <div className="flex flex-col items-center space-y-2">
                <div className="icon-container bg-brand-emerald-100 text-brand-emerald-600">
                  <Star className="h-5 w-5" />
                </div>
                <span className="heading-md text-brand-neutral-800">98%</span>
                <p className="text-body-sm text-muted">Satisfação</p>
              </div>
            </FeatureCard>
          </div>
        </div>

        <div className="relative animate-fade-in order-first lg:order-last">
          <div className="relative z-10 card-pattern p-3 sm:p-4 md:p-6">
            <img
              src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
              alt="Mariah Blaj - Fisioterapeuta Pélvica"
              className="w-full h-64 sm:h-72 md:h-80 lg:h-96 object-cover rounded-2xl"
            />
            <div className="absolute -bottom-3 -right-3 sm:-bottom-4 sm:-right-4 bg-logo-rose text-white p-3 sm:p-4 md:p-6 rounded-2xl">
              <p className="font-semibold text-sm sm:text-base md:text-lg">
                São Paulo
              </p>
              <p className="text-rose-100 text-xs sm:text-sm">
                Atendimento Especializado
              </p>
            </div>
          </div>

          <div className="absolute -top-4 -left-4 sm:-top-6 sm:-left-6 w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 bg-logo-rose/20 rounded-3xl animate-float" />
          <div
            className="absolute -bottom-4 -right-4 sm:-bottom-6 sm:-right-6 w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 bg-logo-emerald/20 rounded-3xl animate-float"
            style={{ animationDelay: "2s" }}
          />
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
