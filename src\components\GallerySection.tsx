
import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Camera } from "lucide-react";

const galleryImages = [
  {
    url: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    title: "Atendimento Personalizado",
    description: "Cuidado individual e humanizado"
  },
  {
    url: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    title: "Ambiente Acolhedor",
    description: "Espaço preparado para seu bem-estar"
  },
  {
    url: "https://images.unsplash.com/photo-1721322800607-8c38375eef04?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    title: "Consultório Moderno",
    description: "Equipamentos de última geração"
  },
  {
    url: "https://images.unsplash.com/photo-1465146344425-f00d5f5c8f07?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    title: "Ambiente Natural",
    description: "Conexão com a natureza"
  },
  {
    url: "https://images.unsplash.com/photo-1500673922987-e212871fec22?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    title: "Tranquilidade",
    description: "Espaço de paz e relaxamento"
  }
];

const GallerySection = () => {
  return (
    <section className="section-spacing bg-gradient-to-br from-logo-rose/5 to-white">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16 animate-fade-in">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Camera className="h-8 w-8 text-logo-emerald" />
            <h2 className="heading-pattern">
              Galeria de <span className="text-logo-emerald">Momentos</span>
            </h2>
          </div>
          <p className="subheading-pattern">
            Conheça nosso espaço e veja como criamos um ambiente acolhedor para seu tratamento
          </p>
        </div>

        <div className="animate-fade-in">
          <Carousel className="w-full max-w-5xl mx-auto">
            <CarouselContent className="-ml-4">
              {galleryImages.map((image, index) => (
                <CarouselItem key={index} className="pl-4 basis-full sm:basis-1/2 lg:basis-1/3">
                  <div className="p-1">
                    <Card className="card-pattern overflow-hidden">
                      <CardContent className="p-0">
                        <div className="relative">
                          <img 
                            src={image.url}
                            alt={image.title}
                            className="w-full h-64 object-cover hover:scale-110 transition-transform duration-300"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300" />
                          <div className="absolute bottom-0 left-0 right-0 p-4 text-white transform translate-y-full hover:translate-y-0 transition-transform duration-300">
                            <h3 className="font-bold text-lg mb-1">{image.title}</h3>
                            <p className="text-sm text-gray-200">{image.description}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="hidden md:flex left-4" />
            <CarouselNext className="hidden md:flex right-4" />
          </Carousel>
        </div>
      </div>
    </section>
  );
};

export default GallerySection;
