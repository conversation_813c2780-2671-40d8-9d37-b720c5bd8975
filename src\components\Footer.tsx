
import { Heart, Instagram, MessageCircle, Mail } from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-gradient-to-br from-white to-logo-emerald/5 text-gray-800 section-spacing border-t border-logo-emerald/10">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
          <div className="space-y-6 text-center md:text-left">
            <h3 className="text-3xl font-playfair font-bold text-logo-emerald">Mariah Blaj</h3>
            <p className="text-gray-600 leading-relaxed text-lg">
              Fisioterapeuta especializada em saúde pélvica, oferecendo cuidado integral 
              e personalizado em São Paulo.
            </p>
            <div className="flex space-x-6 justify-center md:justify-start">
              <a 
                href="https://instagram.com/mariahblaj" 
                className="icon-container bg-logo-emerald text-white hover:scale-110 transition-transform duration-300"
              >
                <Instagram className="h-5 w-5" />
              </a>
              <a 
                href="https://wa.me/5511999999999" 
                className="icon-container bg-logo-emerald text-white hover:scale-110 transition-transform duration-300"
              >
                <MessageCircle className="h-5 w-5" />
              </a>
              <a 
                href="mailto:<EMAIL>" 
                className="icon-container bg-logo-emerald text-white hover:scale-110 transition-transform duration-300"
              >
                <Mail className="h-5 w-5" />
              </a>
            </div>
          </div>
          
          <div className="space-y-6 text-center md:text-left">
            <h4 className="text-xl font-bold text-logo-emerald">Tratamentos</h4>
            <ul className="space-y-3 text-gray-600">
              <li><a href="#services" className="hover:text-logo-emerald transition-colors duration-300">Fisioterapia Pélvica</a></li>
              <li><a href="#services" className="hover:text-logo-emerald transition-colors duration-300">Pilates Terapêutico</a></li>
              <li><a href="#services" className="hover:text-logo-emerald transition-colors duration-300">Yoga Hormonal</a></li>
              <li><a href="#services" className="hover:text-logo-emerald transition-colors duration-300">Aromaterapia</a></li>
              <li><a href="#services" className="hover:text-logo-emerald transition-colors duration-300">Acupuntura</a></li>
            </ul>
          </div>
          
          <div className="space-y-6 text-center md:text-left">
            <h4 className="text-xl font-bold text-logo-emerald">Contato</h4>
            <div className="space-y-3 text-gray-600">
              <p>📍 São Paulo, SP</p>
              <p>📞 (11) 99999-9999</p>
              <p>✉️ <EMAIL></p>
              <div className="pt-2 border-t border-logo-emerald/20">
                <p className="font-semibold text-gray-700 mb-2">Horários:</p>
                <p className="text-sm">Segunda a Sexta: 8h às 18h</p>
                <p className="text-sm">Sábado: 8h às 14h</p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="border-t border-logo-emerald/20 mt-12 pt-8 text-center">
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-3 text-gray-500">
            <span>© 2024 Mariah Blaj - Fisioterapeuta Pélvica.</span>
            <div className="flex items-center space-x-2">
              <span>Feito com</span>
              <Heart className="h-4 w-4 text-logo-rose fill-current" />
              <span>em São Paulo</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
