
import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Star, Quote, Heart } from "lucide-react";

const testimonials = [
  {
    name: "Ana Carolina S.",
    age: 34,
    condition: "Incontinência Urinária",
    text: "Após meses sofrendo em silêncio, encontrei na Mariah não apenas uma profissional excepcional, mas uma pessoa que me acolheu com carinho. Hoje me sinto livre e confiante novamente. O tratamento mudou completamente minha qualidade de vida.",
    rating: 5,
    highlight: "Mudou minha vida completamente"
  },
  {
    name: "<PERSON>",
    age: 28,
    condition: "Preparação para o Parto",
    text: "O trabalho de preparação pélvica que fizemos foi fundamental para um parto tranquilo e natural. A Mariah me deu todas as ferramentas e confiança necessárias. Recomendo para todas as futuras mamães!",
    rating: 5,
    highlight: "Parto natural e tranquilo"
  },
  {
    name: "<PERSON>",
    age: 45,
    condition: "Yoga Hormonal",
    text: "A menopausa estava sendo um pesadelo até conhecer a yoga hormonal. Os fogachos diminuíram drasticamente, meu humor melhorou e voltei a me sentir eu mesma. O ambiente do consultório é simplesmente acolhedor.",
    rating: 5,
    highlight: "Menopausa sem sofrimento"
  },
  {
    name: "Roberta P.",
    age: 32,
    condition: "Pilates Terapêutico",
    text: "Sofria com dores crônicas nas costas há anos. Com o pilates terapêutico, não só as dores desapareceram como ganhei uma consciência corporal incrível. Cada sessão é um momento especial de cuidado comigo mesma.",
    rating: 5,
    highlight: "Fim das dores crônicas"
  },
  {
    name: "Camila R.",
    age: 29,
    condition: "Fisioterapia Pélvica",
    text: "Pensava que teria que conviver com o desconforto para sempre. A Mariah me mostrou que era possível melhorar e hoje me sinto uma nova mulher. Sua abordagem é única, combina técnica com muito acolhimento.",
    rating: 5,
    highlight: "Uma nova mulher"
  },
  {
    name: "Patricia S.",
    age: 38,
    condition: "Aromaterapia",
    text: "A aromaterapia foi um complemento perfeito ao meu tratamento. Me ajudou muito com a ansiedade e o estresse do dia a dia. O ambiente do consultório, com os aromas terapêuticos, é simplesmente relaxante.",
    rating: 5,
    highlight: "Adeus ansiedade"
  }
];

const TestimonialsSection = () => {
  return (
    <section className="section-spacing bg-gradient-to-br from-logo-rose/5 to-logo-emerald/5">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12 md:mb-16 animate-fade-in">
          <div className="badge-pattern inline-flex items-center mb-4 md:mb-6">
            💝 Depoimentos Reais
          </div>
          
          <h2 className="heading-pattern mb-4 md:mb-6">
            Histórias de <span className="text-logo-emerald">Transformação</span>
          </h2>
          
          <p className="subheading-pattern">
            Veja como nossos tratamentos mudaram a vida dessas mulheres incríveis. 
            Cada história é única e especial.
          </p>
        </div>

        <div className="animate-fade-in">
          <Carousel className="w-full max-w-5xl mx-auto">
            <CarouselContent className="-ml-4">
              {testimonials.map((testimonial, index) => (
                <CarouselItem key={index} className="pl-4 basis-full md:basis-1/2">
                  <div className="p-2">
                    <Card className="testimonial-card h-full">
                      <CardContent className="p-6 md:p-8">
                        <div className="flex items-center justify-between mb-4 md:mb-6">
                          <div className="flex space-x-1">
                            {[...Array(testimonial.rating)].map((_, i) => (
                              <Star key={i} className="h-4 w-4 md:h-5 md:w-5 fill-yellow-400 text-yellow-400" />
                            ))}
                          </div>
                          <Quote className="h-6 w-6 md:h-8 md:w-8 text-logo-emerald/20" />
                        </div>
                        
                        <div className="bg-logo-emerald/10 rounded-2xl p-3 md:p-4 mb-4 md:mb-6">
                          <p className="text-sm font-semibold text-logo-emerald mb-1">
                            "{testimonial.highlight}"
                          </p>
                        </div>
                        
                        <p className="text-gray-700 leading-relaxed mb-4 md:mb-6 italic text-sm md:text-base">
                          "{testimonial.text}"
                        </p>
                        
                        <div className="border-t border-gray-100 pt-4 md:pt-6">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-logo-rose to-logo-pink rounded-2xl flex items-center justify-center">
                              <Heart className="h-5 w-5 md:h-6 md:w-6 text-white" />
                            </div>
                            <div>
                              <h4 className="font-bold text-gray-800 text-base md:text-lg">{testimonial.name}</h4>
                              <p className="text-sm text-gray-500">{testimonial.age} anos</p>
                              <p className="text-sm text-logo-emerald font-medium">{testimonial.condition}</p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="hidden md:flex left-4 border-0 bg-white/80 hover:bg-white" />
            <CarouselNext className="hidden md:flex right-4 border-0 bg-white/80 hover:bg-white" />
          </Carousel>
        </div>
        
        <div className="text-center mt-10 md:mt-12">
          <p className="text-base md:text-lg text-gray-600 mb-4 md:mb-6">
            Você também pode ter sua história de transformação
          </p>
          <button 
            onClick={() =>
              document
                .getElementById("contact")
                ?.scrollIntoView({ behavior: "smooth" })
            }
            className="button-primary"
          >
            Começar Minha Transformação
          </button>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
