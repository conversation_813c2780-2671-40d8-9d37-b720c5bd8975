import * as React from "react";
import { cn } from "@/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";

const cardVariants = cva(
  "rounded-2xl border transition-all duration-300",
  {
    variants: {
      variant: {
        default: "card-pattern",
        elevated: "card-elevated",
        glass: "card-glass",
        service: "service-card",
        testimonial: "testimonial-card",
        feature: "feature-card",
        gallery: "gallery-card",
        outline: "bg-transparent border-2 border-brand-neutral-200 hover:border-brand-neutral-300",
        ghost: "bg-transparent border-0 hover:bg-brand-neutral-50",
      },
      padding: {
        none: "p-0",
        sm: "p-4",
        default: "p-6",
        lg: "p-8",
        xl: "p-10",
      },
      interactive: {
        true: "cursor-pointer interactive-lift",
        false: "",
      },
      fullHeight: {
        true: "h-full",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      padding: "default",
      interactive: false,
      fullHeight: false,
    },
  }
);

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  asChild?: boolean;
}

const EnhancedCard = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, padding, interactive, fullHeight, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(cardVariants({ variant, padding, interactive, fullHeight, className }))}
      {...props}
    />
  )
);
EnhancedCard.displayName = "EnhancedCard";

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
));
CardHeader.displayName = "CardHeader";

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn("heading-md", className)}
    {...props}
  />
));
CardTitle.displayName = "CardTitle";

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-body text-muted", className)}
    {...props}
  />
));
CardDescription.displayName = "CardDescription";

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
));
CardContent.displayName = "CardContent";

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center p-6 pt-0", className)}
    {...props}
  />
));
CardFooter.displayName = "CardFooter";

// Specialized card components
const ServiceCard = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, children, ...props }, ref) => (
    <EnhancedCard
      ref={ref}
      variant="service"
      padding="none"
      fullHeight
      className={className}
      {...props}
    >
      {children}
    </EnhancedCard>
  )
);
ServiceCard.displayName = "ServiceCard";

const TestimonialCard = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, children, ...props }, ref) => (
    <EnhancedCard
      ref={ref}
      variant="testimonial"
      padding="none"
      fullHeight
      className={className}
      {...props}
    >
      {children}
    </EnhancedCard>
  )
);
TestimonialCard.displayName = "TestimonialCard";

const FeatureCard = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, children, interactive = true, ...props }, ref) => (
    <EnhancedCard
      ref={ref}
      variant="feature"
      interactive={interactive}
      fullHeight
      className={className}
      {...props}
    >
      {children}
    </EnhancedCard>
  )
);
FeatureCard.displayName = "FeatureCard";

const GalleryCard = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, children, ...props }, ref) => (
    <EnhancedCard
      ref={ref}
      variant="gallery"
      padding="none"
      className={className}
      {...props}
    >
      {children}
    </EnhancedCard>
  )
);
GalleryCard.displayName = "GalleryCard";

export {
  EnhancedCard,
  CardHeader,
  CardFooter,
  CardTitle,
  CardDescription,
  CardContent,
  ServiceCard,
  TestimonialCard,
  FeatureCard,
  GalleryCard,
  cardVariants,
};
