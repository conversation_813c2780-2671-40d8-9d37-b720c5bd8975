import * as React from "react";
import { cn } from "@/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";
import { Eye, EyeOff, AlertCircle, CheckCircle, Info } from "lucide-react";

const inputVariants = cva(
  "flex w-full rounded-2xl border bg-white px-4 py-3 text-base transition-all duration-300 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-brand-neutral-400 focus-ring disabled:cursor-not-allowed disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "border-brand-neutral-300 hover:border-brand-neutral-400 focus:border-brand-rose-500",
        error: "border-error-500 focus:border-error-500 bg-error-50/50",
        success: "border-success-500 focus:border-success-500 bg-success-50/50",
        warning: "border-warning-500 focus:border-warning-500 bg-warning-50/50",
      },
      size: {
        sm: "h-9 px-3 py-2 text-sm",
        default: "h-11 px-4 py-3 text-base",
        lg: "h-13 px-5 py-4 text-lg",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "size">,
    VariantProps<typeof inputVariants> {
  label?: string;
  helperText?: string;
  errorMessage?: string;
  successMessage?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  showPasswordToggle?: boolean;
}

const EnhancedInput = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      variant,
      size,
      type,
      label,
      helperText,
      errorMessage,
      successMessage,
      leftIcon,
      rightIcon,
      showPasswordToggle = false,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = React.useState(false);
    const [inputType, setInputType] = React.useState(type);

    React.useEffect(() => {
      if (showPasswordToggle && type === "password") {
        setInputType(showPassword ? "text" : "password");
      } else {
        setInputType(type);
      }
    }, [showPassword, type, showPasswordToggle]);

    // Determine the actual variant based on validation state
    const actualVariant = errorMessage
      ? "error"
      : successMessage
      ? "success"
      : variant;

    const inputId = React.useId();

    return (
      <div className="space-y-2">
        {label && (
          <label
            htmlFor={inputId}
            className="block text-sm font-medium text-brand-neutral-700"
          >
            {label}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-brand-neutral-400">
              {leftIcon}
            </div>
          )}
          
          <input
            id={inputId}
            type={inputType}
            className={cn(
              inputVariants({ variant: actualVariant, size }),
              leftIcon && "pl-10",
              (rightIcon || showPasswordToggle || errorMessage || successMessage) && "pr-10",
              className
            )}
            ref={ref}
            {...props}
          />
          
          <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center space-x-1">
            {showPasswordToggle && type === "password" && (
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="text-brand-neutral-400 hover:text-brand-neutral-600 transition-colors"
                tabIndex={-1}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            )}
            
            {errorMessage && (
              <AlertCircle className="h-4 w-4 text-error-500" />
            )}
            
            {successMessage && (
              <CheckCircle className="h-4 w-4 text-success-500" />
            )}
            
            {rightIcon && !errorMessage && !successMessage && (
              <div className="text-brand-neutral-400">{rightIcon}</div>
            )}
          </div>
        </div>
        
        {(helperText || errorMessage || successMessage) && (
          <div className="flex items-start space-x-1">
            {errorMessage && (
              <>
                <AlertCircle className="h-4 w-4 text-error-500 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-error-600">{errorMessage}</p>
              </>
            )}
            
            {successMessage && !errorMessage && (
              <>
                <CheckCircle className="h-4 w-4 text-success-500 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-success-600">{successMessage}</p>
              </>
            )}
            
            {helperText && !errorMessage && !successMessage && (
              <>
                <Info className="h-4 w-4 text-brand-neutral-400 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-brand-neutral-600">{helperText}</p>
              </>
            )}
          </div>
        )}
      </div>
    );
  }
);

EnhancedInput.displayName = "EnhancedInput";

export { EnhancedInput, inputVariants };
