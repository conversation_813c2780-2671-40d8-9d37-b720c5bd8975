import React from "react";
import { cn } from "@/lib/utils";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-3xl font-medium transition-all duration-300 focus-ring disabled:pointer-events-none disabled:opacity-50 active:scale-95",
  {
    variants: {
      variant: {
        primary: "button-primary",
        secondary: "button-secondary",
        emerald: "button-emerald",
        ghost: "button-ghost",
        outline:
          "border-2 border-brand-neutral-300 text-brand-neutral-700 hover:bg-brand-neutral-50 hover:border-brand-neutral-400 interactive-scale",
        gradient:
          "gradient-rose text-white hover:shadow-brand-lg interactive-scale",
        "gradient-emerald":
          "gradient-emerald text-white hover:shadow-emerald-lg interactive-scale",
        glass:
          "bg-white/80 backdrop-blur-md border border-white/20 text-brand-neutral-700 hover:bg-white/90 shadow-lg hover:shadow-xl interactive-scale",
        destructive:
          "bg-error-500 hover:bg-error-600 text-white shadow-sm hover:shadow-md interactive-scale",
        success:
          "bg-success-500 hover:bg-success-600 text-white shadow-sm hover:shadow-md interactive-scale",
        warning:
          "bg-warning-500 hover:bg-warning-600 text-white shadow-sm hover:shadow-md interactive-scale",
      },
      size: {
        xs: "h-8 px-2 text-xs",
        sm: "h-9 px-3 text-xs",
        default: "h-10 px-4 py-2 sm:px-6 sm:py-3 text-sm",
        lg: "h-12 px-6 py-3 sm:px-8 sm:py-4 text-base",
        xl: "h-14 px-8 py-4 sm:px-10 sm:py-5 text-lg",
        "2xl": "h-16 px-10 py-5 sm:px-12 sm:py-6 text-xl",
        icon: "h-10 w-10",
        "icon-sm": "h-8 w-8",
        "icon-lg": "h-12 w-12",
      },
      loading: {
        true: "cursor-not-allowed",
        false: "",
      },
      fullWidth: {
        true: "w-full",
        false: "",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "default",
      loading: false,
      fullWidth: false,
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
  loadingText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  tooltip?: string;
}

const EnhancedButton = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      asChild = false,
      loading = false,
      loadingText,
      leftIcon,
      rightIcon,
      fullWidth = false,
      tooltip,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : "button";
    const isDisabled = disabled || loading;

    const buttonElement = (
      <Comp
        className={cn(
          buttonVariants({ variant, size, loading, fullWidth, className })
        )}
        ref={ref}
        disabled={isDisabled}
        aria-busy={loading}
        title={tooltip}
        {...props}
      >
        {loading && (
          <div className="loading-spinner w-4 h-4 mr-2" aria-hidden="true" />
        )}
        {!loading && leftIcon && (
          <span className="mr-2 flex-shrink-0" aria-hidden="true">
            {leftIcon}
          </span>
        )}
        <span className="truncate">
          {loading && loadingText ? loadingText : children}
        </span>
        {!loading && rightIcon && (
          <span className="ml-2 flex-shrink-0" aria-hidden="true">
            {rightIcon}
          </span>
        )}
      </Comp>
    );

    return buttonElement;
  }
);

EnhancedButton.displayName = "EnhancedButton";

export { EnhancedButton, buttonVariants };
