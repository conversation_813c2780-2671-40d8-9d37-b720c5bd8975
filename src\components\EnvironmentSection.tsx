
import { Card, CardContent } from "@/components/ui/card";
import { Leaf, Heart, Shield, Sparkles, Sun, Wind } from "lucide-react";

const environmentFeatures = [
  {
    icon: Leaf,
    title: "Ambiente Natural",
    description: "Plantas e elementos naturais que promovem tranquilidade e bem-estar"
  },
  {
    icon: Sun,
    title: "Iluminação Natural",
    description: "Luz natural abundante para criar um ambiente acolhedor e energizante"
  },
  {
    icon: Wind,
    title: "Ventilação Pura",
    description: "Sistema de ventilação que garante ar puro e renovado constantemente"
  },
  {
    icon: Shield,
    title: "Privacidade Total",
    description: "Ambiente reservado e discreto para garantir seu conforto e privacidade"
  },
  {
    icon: Heart,
    title: "Energia Positiva",
    description: "Espaço energeticamente harmonizado com cristais e aromas terapêuticos"
  },
  {
    icon: Sparkles,
    title: "Higienização Completa",
    description: "Protocolos rigorosos de limpeza e sanitização entre cada atendimento"
  }
];

const EnvironmentSection = () => {
  return (
    <section className="section-spacing">
      <div className="max-w-6xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <div className="space-y-8 animate-fade-in order-2 lg:order-1">
            <div>
              <h2 className="heading-pattern mb-6 text-center lg:text-left">
                Nosso <span className="text-logo-emerald">Ambiente</span>
              </h2>
              <p className="subheading-pattern text-center lg:text-left mx-auto lg:mx-0">
                Criamos um espaço especialmente pensado para promover seu bem-estar, 
                combinando conforto, privacidade e energia positiva em cada detalhe.
              </p>
            </div>
            
            <div className="grid gap-6">
              {environmentFeatures.map((feature, index) => (
                <div 
                  key={index}
                  className="flex items-start space-x-4 p-4 rounded-2xl bg-white/50 backdrop-blur-sm hover:bg-white/80 transition-all duration-300"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="icon-container bg-logo-emerald/10 flex-shrink-0">
                    <feature.icon className="h-6 w-6 text-logo-emerald" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-800 mb-2">{feature.title}</h3>
                    <p className="text-base text-gray-600 leading-relaxed">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="relative animate-fade-in order-1 lg:order-2">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <img 
                  src="https://images.unsplash.com/photo-1721322800607-8c38375eef04?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="Ambiente do consultório"
                  className="w-full h-48 object-cover rounded-2xl hover:scale-105 transition-transform duration-300"
                />
                <img 
                  src="https://images.unsplash.com/photo-1465146344425-f00d5f5c8f07?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="Decoração natural"
                  className="w-full h-32 object-cover rounded-2xl hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="space-y-4 mt-8">
                <img 
                  src="https://images.unsplash.com/photo-1500673922987-e212871fec22?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="Ambiente relaxante"
                  className="w-full h-32 object-cover rounded-2xl hover:scale-105 transition-transform duration-300"
                />
                <img 
                  src="https://images.unsplash.com/photo-1649972904349-6e44c42644a7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="Espaço de atendimento"
                  className="w-full h-48 object-cover rounded-2xl hover:scale-105 transition-transform duration-300"
                />
              </div>
            </div>
            
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-logo-rose/20 rounded-full animate-float" />
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-logo-emerald/20 rounded-full animate-float" style={{animationDelay: '2s'}} />
          </div>
        </div>
      </div>
    </section>
  );
};

export default EnvironmentSection;
